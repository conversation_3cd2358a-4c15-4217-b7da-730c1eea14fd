#!/usr/bin/env python3
"""
设备信息解析器
用于解析和格式化显示设备返回的基本信息
"""

import json
import datetime
from typing import Dict, Any


class DeviceInfoParser:
    """设备信息解析器类"""
    
    @staticmethod
    def parse_basic_info(data: Dict[str, Any]) -> str:
        """解析设备基本信息并返回格式化字符串"""
        if not isinstance(data, dict):
            return f"无效的数据格式: {data}"
        
        # 检查是否是设备基本信息响应
        if "read" not in data or data["read"] != "basicInfo":
            return DeviceInfoParser._parse_generic_data(data)
        
        result = []
        result.append("📋 设备基本信息详情")
        result.append("=" * 60)
        
        # 设备标识信息
        result.append("🔍 设备标识信息:")
        if "DeviceID" in data:
            result.append(f"  设备ID: {data['DeviceID']}")
        if "IMEI" in data:
            result.append(f"  IMEI: {data['IMEI']}")
        if "IMSI" in data:
            result.append(f"  IMSI: {data['IMSI']}")
        if "ICCID" in data:
            result.append(f"  ICCID: {data['ICCID']}")
        
        # 版本信息
        result.append("\n📦 版本信息:")
        if "version" in data:
            result.append(f"  软件版本: {data['version']}")
        if "hardVer" in data:
            result.append(f"  硬件版本: {data['hardVer']}")
        if "type" in data:
            result.append(f"  设备类型: {data['type']}")
        
        # 网络信息
        result.append("\n📶 网络状态:")
        if "singnal" in data:
            signal_strength = data['singnal']
            signal_desc = DeviceInfoParser._get_signal_description(signal_strength)
            result.append(f"  信号强度: {signal_strength} dBm ({signal_desc})")
        if "csq" in data:
            csq_value = data['csq']
            csq_desc = DeviceInfoParser._get_csq_description(csq_value)
            result.append(f"  信号质量: {csq_value} ({csq_desc})")
        
        # 位置信息
        if "location" in data:
            result.append("\n📍 位置信息:")
            location = data['location']
            if '-' in location:
                try:
                    lat, lon = location.split('-')
                    result.append(f"  纬度: {lat}°")
                    result.append(f"  经度: {lon}°")
                    result.append(f"  坐标: ({lat}, {lon})")
                except:
                    result.append(f"  位置: {location}")
            else:
                result.append(f"  位置: {location}")
        
        # 设备状态
        result.append("\n⚙️  设备状态:")
        if "holdopen" in data:
            holdopen_status = "开启" if data['holdopen'] else "关闭"
            result.append(f"  保持开门: {holdopen_status}")
        if "delayTime" in data:
            result.append(f"  延迟时间: {data['delayTime']}秒")
        if "refuseCopy" in data:
            refuse_status = "开启" if data['refuseCopy'] else "关闭"
            result.append(f"  拒绝复制: {refuse_status}")
        if "keyLock" in data:
            lock_status = "锁定" if data['keyLock'] else "未锁定"
            result.append(f"  按键锁定: {lock_status}")
        
        # 时间戳
        if "timestamp" in data:
            result.append("\n🕐 时间信息:")
            timestamp = data['timestamp']
            try:
                dt = datetime.datetime.fromtimestamp(timestamp)
                result.append(f"  时间戳: {timestamp}")
                result.append(f"  格式化时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
                result.append(f"  相对时间: {DeviceInfoParser._get_relative_time(timestamp)}")
            except:
                result.append(f"  时间戳: {timestamp} (无法解析)")
        
        # 其他信息
        other_keys = set(data.keys()) - {
            'DeviceID', 'IMEI', 'IMSI', 'ICCID', 'version', 'hardVer', 'type',
            'singnal', 'csq', 'location', 'holdopen', 'delayTime', 'refuseCopy',
            'keyLock', 'timestamp', 'read'
        }
        if other_keys:
            result.append("\n📄 其他信息:")
            for key in sorted(other_keys):
                result.append(f"  {key}: {data[key]}")
        
        result.append("=" * 60)
        return "\n".join(result)
    
    @staticmethod
    def _parse_generic_data(data: Dict[str, Any]) -> str:
        """解析通用数据格式"""
        result = []
        result.append("📄 设备数据:")
        result.append("-" * 40)
        
        for key, value in data.items():
            if isinstance(value, dict):
                result.append(f"{key}:")
                for sub_key, sub_value in value.items():
                    result.append(f"  {sub_key}: {sub_value}")
            elif isinstance(value, list):
                result.append(f"{key}: {', '.join(map(str, value))}")
            else:
                result.append(f"{key}: {value}")
        
        result.append("-" * 40)
        return "\n".join(result)
    
    @staticmethod
    def _get_signal_description(signal_strength: int) -> str:
        """获取信号强度描述"""
        if signal_strength >= -70:
            return "优秀"
        elif signal_strength >= -85:
            return "良好"
        elif signal_strength >= -100:
            return "一般"
        else:
            return "较差"
    
    @staticmethod
    def _get_csq_description(csq_value: int) -> str:
        """获取CSQ信号质量描述"""
        if csq_value >= 20:
            return "优秀"
        elif csq_value >= 15:
            return "良好"
        elif csq_value >= 10:
            return "一般"
        elif csq_value >= 5:
            return "较差"
        else:
            return "很差"
    
    @staticmethod
    def _get_relative_time(timestamp: int) -> str:
        """获取相对时间描述"""
        try:
            now = datetime.datetime.now().timestamp()
            diff = now - timestamp
            
            if diff < 60:
                return f"{int(diff)}秒前"
            elif diff < 3600:
                return f"{int(diff/60)}分钟前"
            elif diff < 86400:
                return f"{int(diff/3600)}小时前"
            else:
                return f"{int(diff/86400)}天前"
        except:
            return "未知"
    
    @staticmethod
    def parse_json_string(json_str: str) -> str:
        """解析JSON字符串并返回格式化的设备信息"""
        try:
            data = json.loads(json_str)
            return DeviceInfoParser.parse_basic_info(data)
        except json.JSONDecodeError as e:
            return f"JSON解析错误: {e}\n原始数据: {json_str}"
        except Exception as e:
            return f"解析异常: {e}\n原始数据: {json_str}"
    
    @staticmethod
    def create_summary(data: Dict[str, Any]) -> str:
        """创建设备信息摘要"""
        if not isinstance(data, dict) or "read" not in data:
            return "无效的设备信息数据"
        
        summary_parts = []
        
        # 设备基本信息
        if "DeviceID" in data:
            summary_parts.append(f"设备ID: {data['DeviceID']}")
        
        if "version" in data:
            summary_parts.append(f"版本: {data['version']}")
        
        # 网络状态
        if "singnal" in data:
            signal_desc = DeviceInfoParser._get_signal_description(data['singnal'])
            summary_parts.append(f"信号: {signal_desc}")
        
        # 设备状态
        status_parts = []
        if "holdopen" in data:
            if data['holdopen']:
                status_parts.append("保持开门")
        if "keyLock" in data:
            if data['keyLock']:
                status_parts.append("按键锁定")
        
        if status_parts:
            summary_parts.append(f"状态: {', '.join(status_parts)}")
        else:
            summary_parts.append("状态: 正常")
        
        return " | ".join(summary_parts)


def demo_parser():
    """演示解析器功能"""
    # 示例设备信息数据
    sample_data = {
        "IMEI": "867179076708597",
        "IMSI": "460240412770789",
        "location": "30.57721100-114.36346300",
        "read": "basicInfo",
        "DeviceID": "2427502F70",
        "type": 50,
        "ICCID": "89860841192440170789",
        "hardVer": "ML06240701",
        "version": "1.0.24110101",
        "singnal": -76,
        "csq": 30,
        "timestamp": 1749549034,
        "holdopen": 0,
        "delayTime": 4,
        "refuseCopy": 0,
        "keyLock": 0
    }
    
    print("=== 设备信息解析器演示 ===")
    print()
    
    # 解析完整信息
    print("1. 完整信息解析:")
    print(DeviceInfoParser.parse_basic_info(sample_data))
    print()
    
    # 创建摘要
    print("2. 设备信息摘要:")
    print(DeviceInfoParser.create_summary(sample_data))
    print()
    
    # JSON字符串解析
    print("3. JSON字符串解析:")
    json_str = json.dumps(sample_data, ensure_ascii=False)
    print(DeviceInfoParser.parse_json_string(json_str))


if __name__ == '__main__':
    demo_parser()
