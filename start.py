#!/usr/bin/env python3
"""
启动脚本 - 提供多种运行选项
"""

import sys
import subprocess
import os


def print_menu():
    """打印菜单"""
    print("=== EMQX MQTT API 服务启动菜单 ===")
    print()
    print("1. 安装依赖包")
    print("2. 测试MQTT服务连接")
    print("3. 启动Flask API服务")
    print("4. 启动MQTT客户端监听")
    print("5. 查看配置信息")
    print("0. 退出")
    print()


def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖包安装成功")
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖包安装失败: {e}")


def test_mqtt_service():
    """测试MQTT服务"""
    print("正在测试MQTT服务...")
    try:
        subprocess.check_call([sys.executable, "test_mqtt_service.py"])
    except subprocess.CalledProcessError as e:
        print(f"✗ 测试失败: {e}")


def start_flask_api():
    """启动Flask API服务"""
    print("正在启动Flask API服务...")
    print("服务将在 http://localhost:5000 启动")
    print("按 Ctrl+C 停止服务")
    try:
        subprocess.check_call([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"✗ 启动失败: {e}")


def start_mqtt_client():
    """启动MQTT客户端"""
    print("正在启动MQTT客户端监听...")
    print("按 Ctrl+C 停止监听")
    try:
        subprocess.check_call([sys.executable, "mqtt_client_example.py"])
    except KeyboardInterrupt:
        print("\n监听已停止")
    except subprocess.CalledProcessError as e:
        print(f"✗ 启动失败: {e}")


def show_config():
    """显示配置信息"""
    print("=== 当前配置信息 ===")
    try:
        from config import EMQX_CONFIG, MQTT_TOPICS, DEVICE_COMMANDS
        
        print(f"EMQX服务器: {EMQX_CONFIG['base_url']}")
        print(f"用户名: {EMQX_CONFIG['username']}")
        print(f"密码: {'*' * len(EMQX_CONFIG['password'])}")
        print()
        print("MQTT主题:")
        print(f"  设备发送: {MQTT_TOPICS['device_send']}")
        print(f"  设备接收: {MQTT_TOPICS['device_accept']}")
        print(f"  QoS级别: {MQTT_TOPICS['qos']}")
        print()
        print("可用命令:")
        for cmd_name in DEVICE_COMMANDS.keys():
            print(f"  {cmd_name}")
        
    except ImportError as e:
        print(f"✗ 无法加载配置: {e}")


def main():
    """主函数"""
    while True:
        print_menu()
        
        try:
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("再见!")
                break
            elif choice == '1':
                install_dependencies()
            elif choice == '2':
                test_mqtt_service()
            elif choice == '3':
                start_flask_api()
            elif choice == '4':
                start_mqtt_client()
            elif choice == '5':
                show_config()
            else:
                print("无效选择，请重新输入")
            
            print()
            input("按回车键继续...")
            print()
            
        except KeyboardInterrupt:
            print("\n\n再见!")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            print()


if __name__ == '__main__':
    main()
