#!/usr/bin/env python3
"""
完整的设备信息演示
包括请求设备信息、监听响应、解析显示
"""

import time
import json
import requests
import threading
from mqtt_api_service import EMQXMQTTService
from device_info_parser import DeviceInfoParser


def demo_device_info_with_parser():
    """演示设备信息读取和解析"""
    print("=== 完整设备信息演示 ===")
    print()
    
    # 1. 发送设备信息读取请求
    print("1. 发送设备信息读取请求...")
    mqtt_service = EMQXMQTTService()
    result = mqtt_service.read_device_basic_info()
    
    if result['success']:
        print(f"✓ 设备信息读取命令发送成功")
        print(f"  消息ID: {result['data']['id']}")
        print("  等待设备响应...")
    else:
        print(f"✗ 设备信息读取命令发送失败: {result['error']}")
        return
    
    print()
    
    # 2. 模拟设备返回的数据（基于您提供的实际数据）
    print("2. 模拟设备返回的数据解析...")
    device_response = {
        "IMEI": "867179076708597",
        "IMSI": "460240412770789", 
        "location": "30.57721100-114.36346300",
        "read": "basicInfo",
        "DeviceID": "2427502F70",
        "type": 50,
        "ICCID": "89860841192440170789",
        "hardVer": "ML06240701",
        "version": "1.0.24110101",
        "singnal": -76,
        "csq": 30,
        "timestamp": int(time.time()),  # 使用当前时间戳
        "holdopen": 0,
        "delayTime": 4,
        "refuseCopy": 0,
        "keyLock": 0
    }
    
    print("📨 收到设备响应:")
    print(f"主题: /devicesend/2427502F70")
    print(f"QoS: 0")
    print(f"原始JSON数据:")
    print(json.dumps(device_response, indent=2, ensure_ascii=False))
    print()
    
    # 3. 使用解析器解析数据
    print("3. 解析设备信息...")
    parsed_info = DeviceInfoParser.parse_basic_info(device_response)
    print(parsed_info)
    print()
    
    # 4. 显示摘要信息
    print("4. 设备信息摘要:")
    summary = DeviceInfoParser.create_summary(device_response)
    print(f"📝 {summary}")
    print()
    
    # 5. 分析设备状态
    print("5. 设备状态分析:")
    analyze_device_status(device_response)
    print()


def analyze_device_status(data):
    """分析设备状态"""
    print("🔍 设备状态分析:")
    
    # 网络状态分析
    if "singnal" in data and "csq" in data:
        signal = data["singnal"]
        csq = data["csq"]
        
        if signal >= -70 and csq >= 20:
            network_status = "优秀"
            network_color = "🟢"
        elif signal >= -85 and csq >= 15:
            network_status = "良好"
            network_color = "🟡"
        elif signal >= -100 and csq >= 10:
            network_status = "一般"
            network_color = "🟠"
        else:
            network_status = "较差"
            network_color = "🔴"
        
        print(f"  {network_color} 网络状态: {network_status}")
        print(f"    信号强度: {signal} dBm")
        print(f"    信号质量: {csq}")
    
    # 设备功能状态
    print("  ⚙️  功能状态:")
    if "holdopen" in data:
        holdopen_status = "🔓 保持开门" if data["holdopen"] else "🔒 正常模式"
        print(f"    {holdopen_status}")
    
    if "keyLock" in data:
        keylock_status = "🔐 按键锁定" if data["keyLock"] else "🔓 按键可用"
        print(f"    {keylock_status}")
    
    if "refuseCopy" in data:
        refuse_status = "🚫 拒绝复制" if data["refuseCopy"] else "✅ 允许复制"
        print(f"    {refuse_status}")
    
    # 时间状态
    if "timestamp" in data:
        import datetime
        device_time = datetime.datetime.fromtimestamp(data["timestamp"])
        current_time = datetime.datetime.now()
        time_diff = abs((current_time - device_time).total_seconds())
        
        if time_diff < 60:
            time_status = "🟢 时间同步正常"
        elif time_diff < 300:
            time_status = "🟡 时间略有偏差"
        else:
            time_status = "🔴 时间偏差较大"
        
        print(f"  {time_status}")
        print(f"    设备时间: {device_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"    时间偏差: {int(time_diff)}秒")
    
    # 位置信息
    if "location" in data:
        location = data["location"]
        if '-' in location:
            try:
                lat, lon = location.split('-')
                lat_f, lon_f = float(lat), float(lon)
                
                # 简单的位置合理性检查（中国境内）
                if 18 <= lat_f <= 54 and 73 <= lon_f <= 135:
                    location_status = "🟢 位置信息正常"
                else:
                    location_status = "🟡 位置信息异常"
                
                print(f"  {location_status}")
                print(f"    坐标: ({lat}°, {lon}°)")
            except:
                print("  🔴 位置信息格式错误")


def demo_api_call():
    """演示API调用方式"""
    print("=== API调用演示 ===")
    print()
    
    try:
        print("通过API请求设备信息...")
        response = requests.post("http://127.0.0.1:5000/device/info")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ API调用成功")
            print(f"  消息ID: {result['data']['id']}")
            print(f"  提示: {result['note']}")
        else:
            print(f"✗ API调用失败: {response.status_code}")
            print(f"  响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到API服务")
        print("  请确保Flask应用正在运行: python3 app.py")
    except Exception as e:
        print(f"✗ API调用异常: {e}")
    
    print()


def demo_multiple_requests():
    """演示多次请求的处理"""
    print("=== 多次请求演示 ===")
    print()
    
    mqtt_service = EMQXMQTTService()
    
    print("连续发送3次设备信息请求...")
    for i in range(1, 4):
        print(f"\n第{i}次请求:")
        result = mqtt_service.read_device_basic_info()
        
        if result['success']:
            print(f"✓ 请求发送成功，消息ID: {result['data']['id']}")
        else:
            print(f"✗ 请求发送失败: {result['error']}")
        
        # 间隔2秒
        if i < 3:
            time.sleep(2)
    
    print("\n注意: 设备可能会返回多个响应，请注意区分不同的消息ID")
    print()


def main():
    """主函数"""
    print("🚪 设备信息完整演示程序")
    print("=" * 50)
    print()
    
    # 演示1: 完整的设备信息处理流程
    demo_device_info_with_parser()
    
    # 演示2: API调用方式
    demo_api_call()
    
    # 演示3: 多次请求处理
    demo_multiple_requests()
    
    print("=" * 50)
    print("📋 总结:")
    print("1. 设备信息读取命令: {\"read\": \"basicInfo\"}")
    print("2. 设备通过MQTT主题异步返回详细信息")
    print("3. 返回的信息包含设备标识、版本、网络、位置、状态等")
    print("4. 可以通过解析器进行格式化显示和状态分析")
    print("5. 支持API调用和直接MQTT操作两种方式")
    print()
    print("💡 建议:")
    print("- 定期读取设备信息进行健康检查")
    print("- 监控网络信号强度和设备状态")
    print("- 记录设备信息变化用于故障诊断")
    print("- 使用专门的监听器实时接收设备响应")


if __name__ == '__main__':
    main()
