"""
MQTT客户端示例
用于测试订阅和接收消息
需要安装: pip install paho-mqtt
"""

import paho.mqtt.client as mqtt
import json
import time
from config import EMQX_CONFIG, MQTT_TOPICS


class MQTTClientExample:
    """MQTT客户端示例类"""
    
    def __init__(self, client_id="python_test_client"):
        self.client_id = client_id
        self.client = mqtt.Client(client_id)
        self.client.username_pw_set(EMQX_CONFIG['username'], EMQX_CONFIG['password'])
        
        # 设置回调函数
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        self.client.on_subscribe = self.on_subscribe
        
        self.connected = False
    
    def on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            print(f"✓ 客户端 {self.client_id} 连接成功")
            self.connected = True
            
            # 自动订阅设备数据主题
            device_send_topic = MQTT_TOPICS['device_send']
            device_accept_topic = MQTT_TOPICS['device_accept']
            qos = MQTT_TOPICS['qos']
            
            print(f"订阅主题: {device_send_topic}")
            client.subscribe(device_send_topic, qos)
            
            print(f"订阅主题: {device_accept_topic}")
            client.subscribe(device_accept_topic, qos)
            
        else:
            print(f"✗ 连接失败，错误代码: {rc}")
            self.connected = False
    
    def on_message(self, client, userdata, msg):
        """消息接收回调"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            
            print(f"\n📨 收到消息:")
            print(f"  主题: {topic}")
            print(f"  QoS: {msg.qos}")
            print(f"  内容: {payload}")
            
            # 尝试解析JSON
            try:
                json_payload = json.loads(payload)
                print(f"  JSON解析: {json.dumps(json_payload, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError:
                print(f"  原始内容: {payload}")
            
            print("-" * 50)
            
        except Exception as e:
            print(f"✗ 处理消息时出错: {e}")
    
    def on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        print(f"✗ 客户端 {self.client_id} 断开连接，代码: {rc}")
        self.connected = False
    
    def on_subscribe(self, client, userdata, mid, granted_qos):
        """订阅成功回调"""
        print(f"✓ 订阅成功，消息ID: {mid}, QoS: {granted_qos}")
    
    def connect(self):
        """连接到EMQX服务器"""
        try:
            host = EMQX_CONFIG['host']
            port = 1883  # MQTT端口，不是HTTP API端口
            
            print(f"正在连接到 {host}:{port}...")
            self.client.connect(host, port, 60)
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def start_loop(self):
        """开始消息循环"""
        if self.connect():
            print("开始监听消息...")
            print("按 Ctrl+C 退出")
            try:
                self.client.loop_forever()
            except KeyboardInterrupt:
                print("\n正在断开连接...")
                self.client.disconnect()
                print("已断开连接")
        else:
            print("无法连接到MQTT服务器")
    
    def publish_test_message(self):
        """发布测试消息"""
        if not self.connected:
            print("✗ 客户端未连接")
            return False
        
        topic = MQTT_TOPICS['device_send']
        test_message = {
            "device_id": "test_device_001",
            "temperature": 25.5,
            "humidity": 60.0,
            "timestamp": int(time.time()),
            "status": "online"
        }
        
        try:
            result = self.client.publish(topic, json.dumps(test_message), MQTT_TOPICS['qos'])
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                print(f"✓ 测试消息发布成功到主题: {topic}")
                return True
            else:
                print(f"✗ 消息发布失败，错误代码: {result.rc}")
                return False
        except Exception as e:
            print(f"✗ 发布消息时出错: {e}")
            return False


def main():
    """主函数"""
    print("=== MQTT客户端示例 ===")
    print("此客户端将订阅设备主题并显示接收到的消息")
    print()
    
    # 创建客户端实例
    client = MQTTClientExample()
    
    # 开始监听
    client.start_loop()


if __name__ == '__main__':
    main()
