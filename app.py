"""
Flask Web API 应用程序
提供HTTP接口来控制MQTT设备
"""

from flask import Flask, request, jsonify
import logging
from mqtt_api_service import EMQXMQTTService
from config import FLASK_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 创建MQTT服务实例
mqtt_service = EMQXMQTTService()


@app.route('/', methods=['GET'])
def home():
    """首页"""
    return jsonify({
        'message': 'EMQX MQTT API 服务',
        'version': '1.0.0',
        'endpoints': {
            'GET /': '首页',
            'POST /door/open': '开门',
            'POST /door/close': '关门',
            'POST /publish': '发布消息',
            'POST /subscribe': '创建订阅',
            'GET /clients': '获取客户端列表',
            'GET /clients/<clientid>': '获取客户端信息',
            'GET /clients/<clientid>/subscriptions': '获取客户端订阅'
        }
    })


@app.route('/door/open', methods=['POST'])
def open_door():
    """开门命令"""
    try:
        result = mqtt_service.send_door_command('open_door')
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '开门命令发送成功',
                'data': result['data']
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': '开门命令发送失败',
                'error': result['error']
            }), result['status_code']
    
    except Exception as e:
        logger.error(f"开门操作异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': '开门操作异常',
            'error': str(e)
        }), 500


@app.route('/door/close', methods=['POST'])
def close_door():
    """关门命令"""
    try:
        result = mqtt_service.send_door_command('close_door')
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '关门命令发送成功',
                'data': result['data']
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': '关门命令发送失败',
                'error': result['error']
            }), result['status_code']
    
    except Exception as e:
        logger.error(f"关门操作异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': '关门操作异常',
            'error': str(e)
        }), 500


@app.route('/publish', methods=['POST'])
def publish_message():
    """发布MQTT消息"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        topic = data.get('topic')
        payload = data.get('payload')
        qos = data.get('qos', 1)
        
        if not topic or not payload:
            return jsonify({
                'success': False,
                'message': 'topic和payload参数是必需的'
            }), 400
        
        result = mqtt_service.publish_message(topic, payload, qos)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '消息发布成功',
                'data': result['data']
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': '消息发布失败',
                'error': result['error']
            }), result['status_code']
    
    except Exception as e:
        logger.error(f"发布消息异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': '发布消息异常',
            'error': str(e)
        }), 500


@app.route('/subscribe', methods=['POST'])
def create_subscription():
    """创建订阅"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        clientid = data.get('clientid')
        topic = data.get('topic')
        qos = data.get('qos', 1)
        
        if not clientid or not topic:
            return jsonify({
                'success': False,
                'message': 'clientid和topic参数是必需的'
            }), 400
        
        result = mqtt_service.create_subscription(clientid, topic, qos)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '订阅创建成功',
                'data': result['data']
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': '订阅创建失败',
                'error': result['error']
            }), result['status_code']
    
    except Exception as e:
        logger.error(f"创建订阅异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': '创建订阅异常',
            'error': str(e)
        }), 500


@app.route('/clients', methods=['GET'])
def get_clients():
    """获取客户端列表"""
    try:
        result = mqtt_service.get_clients()
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '获取客户端列表成功',
                'data': result['data']
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': '获取客户端列表失败',
                'error': result['error']
            }), result['status_code']
    
    except Exception as e:
        logger.error(f"获取客户端列表异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取客户端列表异常',
            'error': str(e)
        }), 500


@app.route('/clients/<clientid>', methods=['GET'])
def get_client_info(clientid):
    """获取客户端信息"""
    try:
        result = mqtt_service.get_client_info(clientid)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '获取客户端信息成功',
                'data': result['data']
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': '获取客户端信息失败',
                'error': result['error']
            }), result['status_code']
    
    except Exception as e:
        logger.error(f"获取客户端信息异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取客户端信息异常',
            'error': str(e)
        }), 500


@app.route('/clients/<clientid>/subscriptions', methods=['GET'])
def get_client_subscriptions(clientid):
    """获取客户端订阅"""
    try:
        result = mqtt_service.get_subscriptions(clientid)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '获取客户端订阅成功',
                'data': result['data']
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': '获取客户端订阅失败',
                'error': result['error']
            }), result['status_code']
    
    except Exception as e:
        logger.error(f"获取客户端订阅异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取客户端订阅异常',
            'error': str(e)
        }), 500


if __name__ == '__main__':
    logger.info("启动EMQX MQTT API服务...")
    app.run(
        host=FLASK_CONFIG['host'],
        port=FLASK_CONFIG['port'],
        debug=FLASK_CONFIG['debug']
    )
