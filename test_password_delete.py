#!/usr/bin/env python3
"""
密码删除功能测试脚本
"""

import time
import requests
from mqtt_api_service import EMQXMQTTService


def test_password_delete_validation():
    """测试密码删除验证功能"""
    print("=== 密码删除验证测试 ===")
    
    mqtt_service = EMQXMQTTService()
    
    # 测试1: 删除单组密码
    print("1. 测试删除单组密码...")
    result = mqtt_service.delete_device_password("123456")
    if result['success']:
        print("✓ 单组密码删除成功")
    else:
        print(f"✗ 单组密码删除失败: {result['error']}")
    
    # 测试2: 删除多组密码
    print("2. 测试删除多组密码...")
    result = mqtt_service.delete_device_password("123456789012345678")
    if result['success']:
        print("✓ 多组密码删除成功（3组密码）")
    else:
        print(f"✗ 多组密码删除失败: {result['error']}")
    
    # 测试3: 删除所有密码
    print("3. 测试删除所有密码...")
    result = mqtt_service.delete_device_password("all")
    if result['success']:
        print("✓ 删除所有密码成功")
    else:
        print(f"✗ 删除所有密码失败: {result['error']}")
    
    # 测试4: 无效密码长度
    print("4. 测试无效密码长度...")
    result = mqtt_service.delete_device_password("12345")  # 5位数字
    if not result['success']:
        print("✓ 正确拒绝了无效长度的密码")
    else:
        print("✗ 应该拒绝无效长度的密码")
    
    # 测试5: 非数字密码
    print("5. 测试非数字密码...")
    result = mqtt_service.delete_device_password("12345a")
    if not result['success']:
        print("✓ 正确拒绝了非数字密码")
    else:
        print("✗ 应该拒绝非数字密码")
    
    # 测试6: 空密码
    print("6. 测试空密码...")
    result = mqtt_service.delete_device_password("")
    if not result['success']:
        print("✓ 正确拒绝了空密码")
    else:
        print("✗ 应该拒绝空密码")
    
    # 测试7: 超过20组密码
    print("7. 测试超过20组密码...")
    long_passwords = "123456" * 21  # 21组密码
    result = mqtt_service.delete_device_password(long_passwords)
    if not result['success']:
        print("✓ 正确拒绝了超过20组的密码")
    else:
        print("✗ 应该拒绝超过20组的密码")
    
    print()


def test_password_delete_with_id():
    """测试带指令ID的密码删除"""
    print("=== 带指令ID的密码删除测试 ===")
    
    mqtt_service = EMQXMQTTService()
    
    # 测试1: 带自定义ID的密码删除
    print("1. 测试带自定义ID的密码删除...")
    result = mqtt_service.delete_device_password(
        passwords="111111",
        command_id="del_test_001"
    )
    if result['success']:
        print("✓ 带自定义ID的密码删除成功")
        print(f"  指令ID: del_test_001")
    else:
        print(f"✗ 带自定义ID的密码删除失败: {result['error']}")
    
    # 测试2: 带自定义ID的删除所有密码
    print("2. 测试带自定义ID的删除所有密码...")
    result = mqtt_service.delete_device_password(
        passwords="all",
        command_id="del_all_001"
    )
    if result['success']:
        print("✓ 带自定义ID的删除所有密码成功")
        print(f"  指令ID: del_all_001")
    else:
        print(f"✗ 带自定义ID的删除所有密码失败: {result['error']}")
    
    # 测试3: 指令ID过长
    print("3. 测试指令ID过长...")
    result = mqtt_service.delete_device_password(
        passwords="222222",
        command_id="this_id_is_too_long_for_the_system"  # 超过13个字符
    )
    if not result['success']:
        print("✓ 正确拒绝了过长的指令ID")
    else:
        print("✗ 应该拒绝过长的指令ID")
    
    print()


def test_api_endpoints():
    """测试API端点"""
    print("=== 密码删除API端点测试 ===")
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试1: 删除单组密码API
    print("1. 测试删除单组密码API...")
    data = {
        "passwords": "555555",
        "id": "api_del_001"
    }
    
    try:
        response = requests.post(f"{base_url}/password/delete", json=data)
        if response.status_code == 200:
            result = response.json()
            print("✓ 删除单组密码API成功")
            print(f"  响应: {result['message']}")
        else:
            print(f"✗ 删除单组密码API失败: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到API服务，请确保Flask应用正在运行")
        return
    
    # 测试2: 删除多组密码API
    print("2. 测试删除多组密码API...")
    data = {
        "passwords": "666666777777888888",  # 3组密码
        "id": "api_del_002"
    }
    
    try:
        response = requests.post(f"{base_url}/password/delete", json=data)
        if response.status_code == 200:
            result = response.json()
            print("✓ 删除多组密码API成功")
            print(f"  响应: {result['message']}")
        else:
            print(f"✗ 删除多组密码API失败: {response.status_code}")
    except Exception as e:
        print(f"✗ API请求异常: {e}")
    
    # 测试3: 删除所有密码API
    print("3. 测试删除所有密码API...")
    data = {
        "passwords": "all",
        "id": "api_del_all"
    }
    
    try:
        response = requests.post(f"{base_url}/password/delete", json=data)
        if response.status_code == 200:
            result = response.json()
            print("✓ 删除所有密码API成功")
            print(f"  响应: {result['message']}")
        else:
            print(f"✗ 删除所有密码API失败: {response.status_code}")
    except Exception as e:
        print(f"✗ API请求异常: {e}")
    
    # 测试4: 无效密码API测试
    print("4. 测试无效密码删除API...")
    data = {
        "passwords": "12345",  # 无效长度
        "id": "api_del_003"
    }
    
    try:
        response = requests.post(f"{base_url}/password/delete", json=data)
        if response.status_code == 400:
            result = response.json()
            print("✓ 正确拒绝了无效密码")
            print(f"  错误信息: {result['message']}")
        else:
            print(f"✗ 应该拒绝无效密码，但返回了: {response.status_code}")
    except Exception as e:
        print(f"✗ API请求异常: {e}")
    
    print()


def demo_password_delete_scenarios():
    """演示密码删除使用场景"""
    print("=== 密码删除使用场景演示 ===")
    
    mqtt_service = EMQXMQTTService()
    
    # 场景1: 删除过期的临时密码
    print("1. 删除过期的临时密码...")
    result = mqtt_service.delete_device_password(
        passwords="100001",  # 之前设置的临时访客密码
        command_id="del_temp_001"
    )
    if result['success']:
        print("✓ 过期临时密码删除成功")
    else:
        print(f"✗ 过期临时密码删除失败: {result['error']}")
    
    # 场景2: 删除离职员工的密码
    print("2. 删除离职员工的密码...")
    result = mqtt_service.delete_device_password(
        passwords="200002300003",  # 删除2组员工密码
        command_id="del_staff_001"
    )
    if result['success']:
        print("✓ 离职员工密码删除成功（2组密码）")
    else:
        print(f"✗ 离职员工密码删除失败: {result['error']}")
    
    # 场景3: 安全事件后清空所有密码
    print("3. 安全事件后清空所有密码...")
    result = mqtt_service.delete_device_password(
        passwords="all",
        command_id="sec_clear_001"  # 缩短ID长度
    )
    if result['success']:
        print("✓ 安全清空所有密码成功")
    else:
        print(f"✗ 安全清空所有密码失败: {result['error']}")
    
    print()


def main():
    """主函数"""
    print("=== 密码删除功能测试 ===")
    print()
    
    # 运行各种测试
    test_password_delete_validation()
    test_password_delete_with_id()
    test_api_endpoints()
    demo_password_delete_scenarios()
    
    print("=== 测试完成 ===")
    print()
    print("密码删除功能说明:")
    print("- 支持删除单组或多组6位数字密码")
    print("- 支持删除所有密码（使用 'all'）")
    print("- 单次最多删除20组密码")
    print("- 支持自定义指令序列ID（最大长度13）")
    print("- 完整的参数验证和错误处理")


if __name__ == '__main__':
    main()
