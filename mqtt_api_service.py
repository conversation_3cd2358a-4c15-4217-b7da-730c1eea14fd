"""
EMQX MQTT API 服务类
提供通过 REST API 进行 MQTT 操作的功能
"""

import json
import base64
import urllib.request
import urllib.error
import time
from typing import Dict, Any, Optional
import logging
from config import EMQX_CONFIG, MQTT_TOPICS, DEVICE_COMMANDS

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EMQXMQTTService:
    """EMQX MQTT API 服务类"""
    
    def __init__(self):
        self.base_url = EMQX_CONFIG['base_url']
        self.username = EMQX_CONFIG['username']
        self.password = EMQX_CONFIG['password']
        self.auth_header = self._create_auth_header()
    
    def _create_auth_header(self) -> str:
        """创建认证头"""
        credentials = f"{self.username}:{self.password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"
    
    def _make_request(self, endpoint: str, method: str = 'GET', data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送HTTP请求到EMQX API"""
        url = f"{self.base_url}/{endpoint}"
        
        # 准备请求数据
        request_data = None
        if data:
            request_data = json.dumps(data).encode('utf-8')
        
        # 创建请求
        req = urllib.request.Request(url, data=request_data, method=method)
        req.add_header('Content-Type', 'application/json')
        req.add_header('Authorization', self.auth_header)
        
        try:
            with urllib.request.urlopen(req) as response:
                response_data = json.loads(response.read().decode())
                logger.info(f"API请求成功: {method} {endpoint}")
                return {
                    'success': True,
                    'data': response_data,
                    'status_code': response.status
                }
        except urllib.error.HTTPError as e:
            error_data = {}
            try:
                error_data = json.loads(e.read().decode())
            except:
                error_data = {'message': str(e)}
            
            logger.error(f"API请求失败: {method} {endpoint}, 错误: {error_data}")
            return {
                'success': False,
                'error': error_data,
                'status_code': e.code
            }
        except Exception as e:
            logger.error(f"请求异常: {str(e)}")
            return {
                'success': False,
                'error': {'message': str(e)},
                'status_code': 500
            }
    
    def publish_message(self, topic: str, payload: Dict[str, Any], qos: int = 1) -> Dict[str, Any]:
        """发布MQTT消息"""
        publish_data = {
            'topic': topic,
            'payload': json.dumps(payload),
            'qos': qos,
            'retain': False
        }
        
        result = self._make_request('publish', 'POST', publish_data)
        
        if result['success']:
            logger.info(f"消息发布成功到主题: {topic}")
        else:
            logger.error(f"消息发布失败到主题: {topic}")
        
        return result
    
    def create_subscription(self, clientid: str, topic: str, qos: int = 1) -> Dict[str, Any]:
        """为客户端创建订阅"""
        subscription_data = {
            'topic': topic,
            'qos': qos
        }
        
        endpoint = f"clients/{clientid}/subscribe"
        result = self._make_request(endpoint, 'POST', subscription_data)
        
        if result['success']:
            logger.info(f"订阅创建成功: 客户端 {clientid} 订阅主题 {topic}")
        else:
            logger.error(f"订阅创建失败: 客户端 {clientid} 订阅主题 {topic}")
        
        return result
    
    def get_subscriptions(self, clientid: str) -> Dict[str, Any]:
        """获取客户端的订阅列表"""
        endpoint = f"clients/{clientid}/subscriptions"
        result = self._make_request(endpoint, 'GET')
        
        if result['success']:
            logger.info(f"获取订阅列表成功: 客户端 {clientid}")
        else:
            logger.error(f"获取订阅列表失败: 客户端 {clientid}")
        
        return result
    
    def unsubscribe(self, clientid: str, topic: str) -> Dict[str, Any]:
        """取消客户端订阅"""
        unsubscribe_data = {
            'topic': topic
        }
        
        endpoint = f"clients/{clientid}/unsubscribe"
        result = self._make_request(endpoint, 'POST', unsubscribe_data)
        
        if result['success']:
            logger.info(f"取消订阅成功: 客户端 {clientid} 取消订阅主题 {topic}")
        else:
            logger.error(f"取消订阅失败: 客户端 {clientid} 取消订阅主题 {topic}")
        
        return result
    
    def get_clients(self) -> Dict[str, Any]:
        """获取连接的客户端列表"""
        result = self._make_request('clients', 'GET')
        
        if result['success']:
            logger.info("获取客户端列表成功")
        else:
            logger.error("获取客户端列表失败")
        
        return result
    
    def get_client_info(self, clientid: str) -> Dict[str, Any]:
        """获取特定客户端信息"""
        endpoint = f"clients/{clientid}"
        result = self._make_request(endpoint, 'GET')
        
        if result['success']:
            logger.info(f"获取客户端信息成功: {clientid}")
        else:
            logger.error(f"获取客户端信息失败: {clientid}")
        
        return result
    
    def send_door_command(self, action: str) -> Dict[str, Any]:
        """发送门控制命令"""
        if action not in DEVICE_COMMANDS:
            return {
                'success': False,
                'error': {'message': f'不支持的操作: {action}'},
                'status_code': 400
            }
        
        command = DEVICE_COMMANDS[action].copy()
        topic = MQTT_TOPICS['device_accept']
        qos = MQTT_TOPICS['qos']
        
        return self.publish_message(topic, command, qos)
    
    def subscribe_to_device_data(self, clientid: str) -> Dict[str, Any]:
        """订阅设备数据主题"""
        topic = MQTT_TOPICS['device_send']
        qos = MQTT_TOPICS['qos']

        return self.create_subscription(clientid, topic, qos)

    def set_device_password(self, passwords: str, time_start: int = None,
                           time_stop: int = None, limit: int = None,
                           command_id: str = None) -> Dict[str, Any]:
        """设置设备密码

        Args:
            passwords: 6位数字密码，可多组（字符串长度必须是6的倍数）
            time_start: 可选，密码生效的秒级时间戳
            time_stop: 可选，密码失效的秒级时间戳
            limit: 可选，密码有效使用次数（>=1）
            command_id: 可选，指令序列ID（最大长度13）

        Returns:
            Dict: 操作结果
        """
        # 验证密码格式
        if not passwords or not passwords.isdigit():
            return {
                'success': False,
                'error': {'message': '密码必须是数字字符串'},
                'status_code': 400
            }

        if len(passwords) % 6 != 0:
            return {
                'success': False,
                'error': {'message': '密码长度必须是6的倍数'},
                'status_code': 400
            }

        password_count = len(passwords) // 6
        if password_count > 20:
            return {
                'success': False,
                'error': {'message': '单次最多只能设置20组密码'},
                'status_code': 400
            }

        # 验证使用次数限制
        if limit is not None and limit < 1:
            return {
                'success': False,
                'error': {'message': '使用次数限制必须大于等于1'},
                'status_code': 400
            }

        # 验证指令ID长度
        if command_id and len(command_id) > 13:
            return {
                'success': False,
                'error': {'message': '指令序列ID最大长度为13'},
                'status_code': 400
            }

        # 构建密码设置命令
        command = DEVICE_COMMANDS['set_password'].copy()
        command['value'] = passwords
        command['id'] = command_id or f"pwd_{int(time.time())}"

        # 设置可选参数
        if time_start is not None:
            command['timeStart'] = time_start
        if time_stop is not None:
            command['timeStop'] = time_stop
        if limit is not None:
            command['limit'] = limit

        # 清理None值
        command = {k: v for k, v in command.items() if v is not None}

        topic = MQTT_TOPICS['device_accept']
        qos = MQTT_TOPICS['qos']

        result = self.publish_message(topic, command, qos)

        if result['success']:
            logger.info(f"密码设置命令发送成功: {password_count}组密码")
        else:
            logger.error(f"密码设置命令发送失败")

        return result

    def delete_device_password(self, passwords: str, command_id: str = None) -> Dict[str, Any]:
        """删除设备密码

        Args:
            passwords: 要删除的6位数字密码（可多组）或 "all"（删除所有密码）
            command_id: 可选，指令序列ID（最大长度13）

        Returns:
            Dict: 操作结果
        """
        # 验证密码格式
        if not passwords:
            return {
                'success': False,
                'error': {'message': '密码参数不能为空'},
                'status_code': 400
            }

        # 如果是删除所有密码
        if passwords.lower() == "all":
            password_count = "all"
        else:
            # 验证数字密码格式
            if not passwords.isdigit():
                return {
                    'success': False,
                    'error': {'message': '密码必须是数字字符串或"all"'},
                    'status_code': 400
                }

            if len(passwords) % 6 != 0:
                return {
                    'success': False,
                    'error': {'message': '密码长度必须是6的倍数'},
                    'status_code': 400
                }

            password_count = len(passwords) // 6
            if password_count > 20:
                return {
                    'success': False,
                    'error': {'message': '单次最多只能删除20组密码'},
                    'status_code': 400
                }

        # 验证指令ID长度
        if command_id and len(command_id) > 13:
            return {
                'success': False,
                'error': {'message': '指令序列ID最大长度为13'},
                'status_code': 400
            }

        # 构建密码删除命令
        command = DEVICE_COMMANDS['delete_password'].copy()
        command['value'] = passwords
        command['id'] = command_id or f"del_{int(time.time())}"

        topic = MQTT_TOPICS['device_accept']
        qos = MQTT_TOPICS['qos']

        result = self.publish_message(topic, command, qos)

        if result['success']:
            if passwords.lower() == "all":
                logger.info(f"删除所有密码命令发送成功")
            else:
                logger.info(f"密码删除命令发送成功: {password_count}组密码")
        else:
            logger.error(f"密码删除命令发送失败")

        return result

    def read_device_basic_info(self) -> Dict[str, Any]:
        """读取设备基本信息

        Returns:
            Dict: 操作结果
        """
        # 构建读取基本信息命令
        command = DEVICE_COMMANDS['read_basic_info'].copy()

        topic = MQTT_TOPICS['device_accept']
        qos = MQTT_TOPICS['qos']

        result = self.publish_message(topic, command, qos)

        if result['success']:
            logger.info(f"读取设备基本信息命令发送成功")
        else:
            logger.error(f"读取设备基本信息命令发送失败")

        return result
