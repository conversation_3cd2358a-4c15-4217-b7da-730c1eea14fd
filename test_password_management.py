#!/usr/bin/env python3
"""
密码管理功能测试脚本
"""

import time
import requests
import json
from mqtt_api_service import EMQXMQTTService


def test_password_validation():
    """测试密码验证功能"""
    print("=== 密码验证测试 ===")
    
    mqtt_service = EMQXMQTTService()
    
    # 测试1: 正常的单组密码
    print("1. 测试单组密码...")
    result = mqtt_service.set_device_password("123456")
    if result['success']:
        print("✓ 单组密码设置成功")
    else:
        print(f"✗ 单组密码设置失败: {result['error']}")
    
    # 测试2: 多组密码
    print("2. 测试多组密码...")
    result = mqtt_service.set_device_password("123456789012345678")
    if result['success']:
        print("✓ 多组密码设置成功（3组密码）")
    else:
        print(f"✗ 多组密码设置失败: {result['error']}")
    
    # 测试3: 无效密码长度
    print("3. 测试无效密码长度...")
    result = mqtt_service.set_device_password("12345")  # 5位数字
    if not result['success']:
        print("✓ 正确拒绝了无效长度的密码")
    else:
        print("✗ 应该拒绝无效长度的密码")
    
    # 测试4: 非数字密码
    print("4. 测试非数字密码...")
    result = mqtt_service.set_device_password("12345a")
    if not result['success']:
        print("✓ 正确拒绝了非数字密码")
    else:
        print("✗ 应该拒绝非数字密码")
    
    # 测试5: 超过20组密码
    print("5. 测试超过20组密码...")
    long_passwords = "123456" * 21  # 21组密码
    result = mqtt_service.set_device_password(long_passwords)
    if not result['success']:
        print("✓ 正确拒绝了超过20组的密码")
    else:
        print("✗ 应该拒绝超过20组的密码")
    
    print()


def test_password_with_options():
    """测试带选项的密码设置"""
    print("=== 密码选项测试 ===")
    
    mqtt_service = EMQXMQTTService()
    
    # 测试1: 带时间限制的密码
    print("1. 测试带时间限制的密码...")
    current_time = int(time.time())
    start_time = current_time + 60  # 1分钟后生效
    stop_time = current_time + 3600  # 1小时后失效
    
    result = mqtt_service.set_device_password(
        passwords="111111",
        time_start=start_time,
        time_stop=stop_time,
        command_id="test_time_001"
    )
    if result['success']:
        print("✓ 带时间限制的密码设置成功")
        print(f"  生效时间: {start_time}")
        print(f"  失效时间: {stop_time}")
    else:
        print(f"✗ 带时间限制的密码设置失败: {result['error']}")
    
    # 测试2: 带使用次数限制的密码
    print("2. 测试带使用次数限制的密码...")
    result = mqtt_service.set_device_password(
        passwords="222222",
        limit=5,
        command_id="test_lmt_001"  # 缩短ID长度
    )
    if result['success']:
        print("✓ 带使用次数限制的密码设置成功")
        print("  使用次数限制: 5次")
    else:
        print(f"✗ 带使用次数限制的密码设置失败: {result['error']}")
    
    # 测试3: 完整选项的密码
    print("3. 测试完整选项的密码...")
    result = mqtt_service.set_device_password(
        passwords="333333444444",  # 2组密码
        time_start=current_time,
        time_stop=current_time + 1800,  # 30分钟后失效
        limit=3,
        command_id="test_full_001"
    )
    if result['success']:
        print("✓ 完整选项的密码设置成功")
        print("  密码组数: 2组")
        print("  使用次数限制: 3次")
        print("  有效期: 30分钟")
    else:
        print(f"✗ 完整选项的密码设置失败: {result['error']}")
    
    print()


def test_api_endpoints():
    """测试API端点"""
    print("=== API端点测试 ===")
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试1: 基本密码设置
    print("1. 测试基本密码设置API...")
    data = {
        "passwords": "555555",
        "id": "api_test_001"
    }
    
    try:
        response = requests.post(f"{base_url}/password/set", json=data)
        if response.status_code == 200:
            result = response.json()
            print("✓ 基本密码设置API成功")
            print(f"  响应: {result['message']}")
        else:
            print(f"✗ 基本密码设置API失败: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到API服务，请确保Flask应用正在运行")
        return
    
    # 测试2: 带完整选项的密码设置
    print("2. 测试带完整选项的密码设置API...")
    current_time = int(time.time())
    data = {
        "passwords": "666666777777888888",  # 3组密码
        "timeStart": current_time,
        "timeStop": current_time + 7200,  # 2小时后失效
        "limit": 10,
        "id": "api_test_002"
    }
    
    try:
        response = requests.post(f"{base_url}/password/set", json=data)
        if response.status_code == 200:
            result = response.json()
            print("✓ 带完整选项的密码设置API成功")
            print(f"  响应: {result['message']}")
        else:
            print(f"✗ 带完整选项的密码设置API失败: {response.status_code}")
    except Exception as e:
        print(f"✗ API请求异常: {e}")
    
    # 测试3: 无效密码API测试
    print("3. 测试无效密码API...")
    data = {
        "passwords": "12345",  # 无效长度
        "id": "api_test_003"
    }
    
    try:
        response = requests.post(f"{base_url}/password/set", json=data)
        if response.status_code == 400:
            result = response.json()
            print("✓ 正确拒绝了无效密码")
            print(f"  错误信息: {result['message']}")
        else:
            print(f"✗ 应该拒绝无效密码，但返回了: {response.status_code}")
    except Exception as e:
        print(f"✗ API请求异常: {e}")
    
    print()


def demo_password_scenarios():
    """演示密码使用场景"""
    print("=== 密码使用场景演示 ===")
    
    mqtt_service = EMQXMQTTService()
    current_time = int(time.time())
    
    # 场景1: 临时访客密码
    print("1. 临时访客密码（1小时有效，使用1次）...")
    result = mqtt_service.set_device_password(
        passwords="100001",
        time_start=current_time,
        time_stop=current_time + 3600,  # 1小时后失效
        limit=1,  # 只能使用1次
        command_id="visitor_001"
    )
    if result['success']:
        print("✓ 临时访客密码设置成功")
    else:
        print(f"✗ 临时访客密码设置失败: {result['error']}")
    
    # 场景2: 保洁人员密码（工作日有效，每天3次）
    print("2. 保洁人员密码（24小时有效，使用3次）...")
    result = mqtt_service.set_device_password(
        passwords="200002",
        time_start=current_time,
        time_stop=current_time + 86400,  # 24小时后失效
        limit=3,  # 可以使用3次
        command_id="cleaner_001"
    )
    if result['success']:
        print("✓ 保洁人员密码设置成功")
    else:
        print(f"✗ 保洁人员密码设置失败: {result['error']}")
    
    # 场景3: 家庭成员密码（永久有效，无次数限制）
    print("3. 家庭成员密码（永久有效）...")
    result = mqtt_service.set_device_password(
        passwords="300003400004",  # 2组密码，给2个家庭成员
        command_id="family_001"
    )
    if result['success']:
        print("✓ 家庭成员密码设置成功（2组密码）")
    else:
        print(f"✗ 家庭成员密码设置失败: {result['error']}")
    
    # 场景4: 紧急密码（立即生效，30分钟后失效）
    print("4. 紧急密码（30分钟有效）...")
    result = mqtt_service.set_device_password(
        passwords="999999",
        time_start=current_time,
        time_stop=current_time + 1800,  # 30分钟后失效
        command_id="emergency_001"
    )
    if result['success']:
        print("✓ 紧急密码设置成功")
    else:
        print(f"✗ 紧急密码设置失败: {result['error']}")
    
    print()


def main():
    """主函数"""
    print("=== 密码管理功能测试 ===")
    print()
    
    # 运行各种测试
    test_password_validation()
    test_password_with_options()
    test_api_endpoints()
    demo_password_scenarios()
    
    print("=== 测试完成 ===")
    print()
    print("密码格式说明:")
    print("- 密码必须是6位数字")
    print("- 可以同时设置多组密码（字符串长度必须是6的倍数）")
    print("- 单次最多设置20组密码")
    print("- 支持时间限制（生效时间和失效时间）")
    print("- 支持使用次数限制")
    print("- 支持自定义指令序列ID")


if __name__ == '__main__':
    main()
