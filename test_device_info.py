#!/usr/bin/env python3
"""
设备信息读取功能测试脚本
"""

import time
import requests
from mqtt_api_service import EMQXMQTTService


def test_device_info_basic():
    """测试基本的设备信息读取功能"""
    print("=== 设备信息读取基础测试 ===")
    
    mqtt_service = EMQXMQTTService()
    
    # 测试1: 基本的设备信息读取
    print("1. 测试基本设备信息读取...")
    result = mqtt_service.read_device_basic_info()
    if result['success']:
        print("✓ 设备信息读取命令发送成功")
        print(f"  消息ID: {result['data']['id']}")
        print("  注意: 设备将通过MQTT主题返回详细信息")
    else:
        print(f"✗ 设备信息读取命令发送失败: {result['error']}")
    
    print()


def test_api_endpoint():
    """测试API端点"""
    print("=== 设备信息读取API端点测试 ===")
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试1: 设备信息读取API
    print("1. 测试设备信息读取API...")
    
    try:
        response = requests.post(f"{base_url}/device/info")
        if response.status_code == 200:
            result = response.json()
            print("✓ 设备信息读取API成功")
            print(f"  响应: {result['message']}")
            print(f"  消息ID: {result['data']['id']}")
            print(f"  提示: {result['note']}")
        else:
            print(f"✗ 设备信息读取API失败: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到API服务，请确保Flask应用正在运行")
        return
    except Exception as e:
        print(f"✗ API请求异常: {e}")
    
    print()


def demo_device_info_scenarios():
    """演示设备信息读取使用场景"""
    print("=== 设备信息读取使用场景演示 ===")
    
    mqtt_service = EMQXMQTTService()
    
    # 场景1: 系统启动时读取设备信息
    print("1. 系统启动时读取设备信息...")
    result = mqtt_service.read_device_basic_info()
    if result['success']:
        print("✓ 系统启动设备信息读取成功")
        print("  用途: 获取设备型号、版本、状态等基本信息")
    else:
        print(f"✗ 系统启动设备信息读取失败: {result['error']}")
    
    # 等待2秒
    time.sleep(2)
    
    # 场景2: 定期健康检查
    print("2. 定期健康检查读取设备信息...")
    result = mqtt_service.read_device_basic_info()
    if result['success']:
        print("✓ 健康检查设备信息读取成功")
        print("  用途: 监控设备运行状态，检测异常")
    else:
        print(f"✗ 健康检查设备信息读取失败: {result['error']}")
    
    # 等待2秒
    time.sleep(2)
    
    # 场景3: 故障诊断时读取设备信息
    print("3. 故障诊断时读取设备信息...")
    result = mqtt_service.read_device_basic_info()
    if result['success']:
        print("✓ 故障诊断设备信息读取成功")
        print("  用途: 获取详细设备状态，辅助故障排查")
    else:
        print(f"✗ 故障诊断设备信息读取失败: {result['error']}")
    
    print()


def explain_device_info_response():
    """说明设备信息响应的处理方式"""
    print("=== 设备信息响应处理说明 ===")
    print()
    print("设备信息读取流程:")
    print("1. 发送读取命令: {\"read\": \"basicInfo\"}")
    print("2. 设备接收命令并处理")
    print("3. 设备通过MQTT主题返回详细信息")
    print("4. 客户端需要订阅相应主题来接收返回数据")
    print()
    print("预期返回的设备信息可能包括:")
    print("- 设备型号和版本信息")
    print("- 设备序列号")
    print("- 硬件状态信息")
    print("- 软件版本信息")
    print("- 网络连接状态")
    print("- 电池电量（如适用）")
    print("- 传感器状态")
    print("- 最后更新时间")
    print("- 设备配置信息")
    print()
    print("注意事项:")
    print("- 设备信息通过MQTT异步返回")
    print("- 需要订阅设备发送主题来接收响应")
    print("- 响应时间取决于设备处理速度和网络状况")
    print("- 建议设置合理的超时时间等待响应")
    print()


def test_multiple_requests():
    """测试多次请求设备信息"""
    print("=== 多次设备信息读取测试 ===")
    
    mqtt_service = EMQXMQTTService()
    
    print("连续发送3次设备信息读取请求...")
    
    for i in range(1, 4):
        print(f"第{i}次请求...")
        result = mqtt_service.read_device_basic_info()
        if result['success']:
            print(f"✓ 第{i}次请求发送成功，消息ID: {result['data']['id']}")
        else:
            print(f"✗ 第{i}次请求发送失败: {result['error']}")
        
        # 间隔1秒
        if i < 3:
            time.sleep(1)
    
    print()
    print("注意: 多次请求可能会产生多个响应，请确保正确处理所有返回数据")
    print()


def main():
    """主函数"""
    print("=== 设备信息读取功能测试 ===")
    print()
    
    # 运行各种测试
    test_device_info_basic()
    test_api_endpoint()
    demo_device_info_scenarios()
    test_multiple_requests()
    explain_device_info_response()
    
    print("=== 测试完成 ===")
    print()
    print("设备信息读取功能说明:")
    print("- 发送 {\"read\": \"basicInfo\"} 命令到设备")
    print("- 设备通过MQTT主题异步返回详细信息")
    print("- 支持多种使用场景（启动检查、健康监控、故障诊断）")
    print("- 需要订阅设备发送主题来接收响应数据")
    print("- 响应内容包含设备的完整基本信息")


if __name__ == '__main__':
    main()
