"""
EMQX MQTT API 配置文件
"""

# EMQX 服务器配置
EMQX_CONFIG = {
    'host': '*************',
    'port': 18083,
    'username': '751cec5573ff4fc3',
    'password': '9BBOlkLATTsk2anpI7oglb3Mr3FxXKZdMF9Cl9BfUr3xHO',
    'base_url': 'http://*************:18083/api/v5'
}

# MQTT 主题配置
MQTT_TOPICS = {
    'device_send': '/devicesend/2427502F70',  # 设备发送数据的主题
    'device_accept': '/deviceaccept/2427502F70',  # 设备接收命令的主题
    'qos': 1  # QoS 级别
}

# 设备控制命令模板
DEVICE_COMMANDS = {
    'open_door': {
        "action": "Crldoor",
        "state": "open",
        "holdopen": 0,
        "id": "123456789111"
    },
    'close_door': {
        "action": "Crldoor",
        "state": "close",
        "holdopen": 0,
        "id": "123456789111"
    },
    'set_password': {
        "action": "SetPassword",
        "value": "",  # 6位数字密码，可多组
        "timeStart": None,  # 可选：生效时间戳
        "timeStop": None,   # 可选：失效时间戳
        "limit": None,      # 可选：使用次数限制
        "id": ""           # 指令序列ID
    }
}

# Flask 应用配置
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': True
}
