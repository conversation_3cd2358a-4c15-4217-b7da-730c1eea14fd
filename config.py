"""
EMQX MQTT API 配置文件
"""

# EMQX 服务器配置
EMQX_CONFIG = {
    'host': '*************',
    'port': 18083,
    'username': '751cec5573ff4fc3',
    'password': '9BBOlkLATTsk2anpI7oglb3Mr3FxXKZdMF9Cl9BfUr3xHO',
    'base_url': 'http://*************:18083/api/v5'
}

# MQTT 主题配置
MQTT_TOPICS = {
    'device_send': '/devicesend/2427502F70',  # 设备发送数据的主题
    'device_accept': '/deviceaccept/2427502F70',  # 设备接收命令的主题
    'qos': 1  # QoS 级别
}

# 设备控制命令模板
DEVICE_COMMANDS = {
    'open_door': {
        "action": "Crldoor",
        "state": "open",
        "holdopen": 0,
        "id": "123456789111"
    },
    'close_door': {
        "action": "Crldoor",
        "state": "close",
        "holdopen": 0,
        "id": "123456789111"
    },
    'set_password': {
        "action": "setkey",
        "value": "",  # 6位数字密码，可多组
        "timeStart": None,  # 可选：生效时间戳
        "timeStop": None,   # 可选：失效时间戳
        "limit": None,      # 可选：使用次数限制
        "id": ""           # 指令序列ID
    },
    'delete_password': {
        "action": "delkey",
        "value": "",  # 6位数字密码（可多组）或 "all"（删除所有）
        "id": ""     # 指令序列ID
    },
    'read_basic_info': {
        "read": "basicInfo"  # 读取设备基本信息
    }
}


#电力设备控制单元
POWER_MQTT_TOPICS = {
    'device_send': '/devicesend/243870C626',  # 设备发送数据的主题
    'device_accept': '/deviceaccept/243870C626',  # 设备接收命令的主题
    'qos': 1  # QoS 级别
}

#电力设备控制命令
POWER_DEVICE_COMMANDS = {
    'power_control': {
       "action": "ConctolPower",
        "slot1": "on",#非必下发项，代表是否打开第1路30A继电器开关 on:打开  off:关闭
        "slot2": "on",#非必下发项，代表是否打开第2路30A继电器开关 on:打开  off:关闭
        "slot3": "on",#非必下发项，代表是否打开第3路10A灯控继电器开关on:打开  off:关闭
        "slotall": "on",#非必下发项，优先级最高，如果和slot1，slot2，slot3指令冲突，则优先执行该项on:打开  off:关闭
        "id": "123456789111"
    },
    'close_door': {
        "action": "Crldoor",
        "state": "close",#open为开门，close为关门
        "holdopen": 0,#非必填项配置门禁是否处于常开状态，为0时关闭常开状态，为1时，打开门禁常开状态该项的加入，可以实现在一条指令中实现开门+常开或关门+常闭
        "delayTime": 4,#配置默认智能门锁延迟关门时间，非必填项填写1~14下发，表示延迟开门后延迟1~14秒
        "id": "123456789111"
    },
    'set_TTS': {
       "action": "PlayTTS",
       "content": "欢迎光临",#语音内容，直接输入文字即可
       "vol": 10, #设置音量：输入值0-10，0为静音非必选下发项，当不下发该项时默认为音量10
       "firstPlay": 1, #设置优先播放：0=不优先播放，1=优先播放，代表直接打断现在正在播放的语音，开始新的语音播报非必选下发项，当不下发该项时默认为优先播放
       "loop": 1, #设置重复次数，一般填写1，代表仅播放1次非必选下发项，当不下发该项时默认为播放1次
       "speaker": 0,#设置发音人：0：甜美女声1：青年男声2：沉稳男声3：知性女声4：卡通变音5、萌女声非必选下发项，当不下发该项时默认为甜美女声
       "style": 1,#设置发音风格:0：一字一顿1：平铺直叙2：有声有色非必选下发项，当不下发该项时默认为平铺直叙风格
       "speed": 5,#设置语速:当不下发该项时默认为5,设置范围0-10，默认为5，0为默认值一半，10为默认值一倍非必选下发项，当不下发该项时默认为5
       "intona": 5,#设置语调:设置范围0-10，默认为5，0为默认值一半，10为默认值一倍非必选下发项，当不下发该项时默认为5
       "id": "123456789111"
    },
    'welcom_TTS': {
        "setting": "startVoice",
        "enabled": 1,  #使能开机欢迎语，默认为欢迎光临0：关闭开机欢迎语1：打开开机欢迎语
        "content": "欢迎光临",#语音内容
        "vol": 10,  #设置音量：输入值0-10，0为静音 非必选下发项，当不下发该项时默认为音量10
        "speaker": 1,#设置发音人：0：甜美女声1：青年男声2：沉稳男声3：知性女声4：卡通变音5、萌女声非必选下发项，当不下发该项时默认为甜美女声
        "style": 1,
        "speed": 5,
        "intona": 5,
        "id": "123456789111"    
    },
    'set_task': {
       "setting": "taskconfig",
        "welvoice": "电源已联通, 欢迎您的光临",
        "warn1voice": "您的订单还剩余X分钟, 如需继续, 请提前续单",
        "warn2voice": "您的订单还剩余X分钟, 如需继续, 请提前续单",
        "overvoice": "您的订单已结束, 临走时请带好随身物品, 欢迎您再次光临",
        "warn1time": 30,
        "warn2time": 5,
        "delaytime": 5,
        "addvoice": "续时成功，祝您玩的愉快",    
        "id": "123456789111"
    }
}

# Flask 应用配置
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': True
}
