# EMQX MQTT API 服务

这是一个Python服务，通过EMQX的REST API来执行MQTT操作，包括发布消息、创建订阅和设备控制。

## 功能特性

- 通过REST API发布MQTT消息
- 管理MQTT订阅
- 设备门控制（开门/关门）
- 获取客户端信息
- Flask Web API接口

## 配置信息

### EMQX服务器配置
- 地址: `http://**************:18083`
- 用户名: `751cec5573ff4fc3`
- 密码: `9BBOlkLATTsk2anpI7oglb3Mr3FxXKZdMF9Cl9BfUr3xHO`

### MQTT主题配置
- 设备发送数据主题: `/devicesend/2427502F70`
- 设备接收命令主题: `/deviceaccept/2427502F70`
- QoS级别: 1

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 测试MQTT服务
```bash
python test_mqtt_service.py
```

### 3. 启动Flask API服务
```bash
python app.py
```

服务将在 `http://localhost:5000` 启动

## API接口

### 基础信息
- **GET /** - 获取API信息和可用端点

### 设备控制
- **POST /door/open** - 发送开门命令
- **POST /door/close** - 发送关门命令

### MQTT操作
- **POST /publish** - 发布MQTT消息
  ```json
  {
    "topic": "/deviceaccept/2427502F70",
    "payload": {"action": "test", "message": "hello"},
    "qos": 1
  }
  ```

- **POST /subscribe** - 创建订阅
  ```json
  {
    "clientid": "client001",
    "topic": "/devicesend/2427502F70",
    "qos": 1
  }
  ```

### 客户端管理
- **GET /clients** - 获取所有连接的客户端
- **GET /clients/{clientid}** - 获取特定客户端信息
- **GET /clients/{clientid}/subscriptions** - 获取客户端的订阅列表

## 使用示例

### 1. 开门命令
```bash
curl -X POST http://localhost:5000/door/open
```

### 2. 关门命令
```bash
curl -X POST http://localhost:5000/door/close
```

### 3. 发布自定义消息
```bash
curl -X POST http://localhost:5000/publish \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "/deviceaccept/2427502F70",
    "payload": {
      "action": "Crldoor",
      "state": "open",
      "holdopen": 0,
      "id": "123456789111"
    },
    "qos": 1
  }'
```

### 4. 获取客户端列表
```bash
curl http://localhost:5000/clients
```

## 门控制命令格式

开门命令:
```json
{
  "action": "Crldoor",
  "state": "open",
  "holdopen": 0,
  "id": "123456789111"
}
```

关门命令:
```json
{
  "action": "Crldoor",
  "state": "close",
  "holdopen": 0,
  "id": "123456789111"
}
```

## 文件结构

```
.
├── app.py                 # Flask Web API应用
├── mqtt_api_service.py    # MQTT API服务类
├── config.py             # 配置文件
├── test_mqtt_service.py  # 测试脚本
├── requirements.txt      # Python依赖
├── README.md            # 说明文档
└── testapi.py           # 原始测试文件（已更新）
```

## 注意事项

1. 确保EMQX服务器可访问
2. 确认API密钥和密码正确
3. 检查网络连接和防火墙设置
4. 客户端必须先连接到EMQX才能创建订阅

## 错误处理

服务包含完整的错误处理机制，所有API调用都会返回标准的JSON响应格式：

成功响应:
```json
{
  "success": true,
  "message": "操作成功",
  "data": {...}
}
```

错误响应:
```json
{
  "success": false,
  "message": "操作失败",
  "error": {...}
}
```
