import urllib.request
import json
import base64

username = '751cec5573ff4fc3'
password = '9BBOlkLATTsk2anpI7oglb3Mr3FxXKZdMF9Cl9BfUr3xHO'

url = 'http://10.15.160.130:18083/api/v5/nodes'

req = urllib.request.Request(url)
req.add_header('Content-Type', 'application/json')

auth_header = "Basic " + base64.b64encode((username + ":" + password).encode()).decode()
req.add_header('Authorization', auth_header)

with urllib.request.urlopen(req) as response:
    data = json.loads(response.read().decode())

print(data)
