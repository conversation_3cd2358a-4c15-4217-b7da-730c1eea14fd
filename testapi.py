import urllib.request
import json
import base64

# 更新为您提供的EMQX服务器地址和凭据
username = '751cec5573ff4fc3'
password = '9BBOlkLATTsk2anpI7oglb3Mr3FxXKZdMF9Cl9BfUr3xHO'

# 更新为您的EMQX服务器地址
url = 'http://122.191.109.33:18083/api/v5/nodes'

req = urllib.request.Request(url)
req.add_header('Content-Type', 'application/json')

auth_header = "Basic " + base64.b64encode((username + ":" + password).encode()).decode()
req.add_header('Authorization', auth_header)

with urllib.request.urlopen(req) as response:
    data = json.loads(response.read().decode())

print(data)
