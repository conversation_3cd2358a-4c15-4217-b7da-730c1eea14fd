#!/usr/bin/env python3
"""
设备信息监听器
专门用于监听和显示设备返回的基本信息
"""

import paho.mqtt.client as mqtt
import json
import time
import requests
from config import EMQX_CONFIG, MQTT_TOPICS
from device_info_parser import DeviceInfoParser


class DeviceInfoListener:
    """设备信息监听器类"""
    
    def __init__(self, client_id="device_info_listener"):
        self.client_id = client_id
        self.client = mqtt.Client(client_id)
        self.client.username_pw_set(EMQX_CONFIG['username'], EMQX_CONFIG['password'])
        
        # 设置回调函数
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        self.client.on_subscribe = self.on_subscribe
        
        self.connected = False
        self.device_info_received = False
    
    def on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            print(f"✓ 设备信息监听器 {self.client_id} 连接成功")
            self.connected = True
            
            # 订阅设备发送数据的主题
            device_send_topic = MQTT_TOPICS['device_send']
            qos = MQTT_TOPICS['qos']
            
            print(f"订阅设备数据主题: {device_send_topic}")
            client.subscribe(device_send_topic, qos)
            
        else:
            print(f"✗ 连接失败，错误代码: {rc}")
            self.connected = False
    
    def on_message(self, client, userdata, msg):
        """消息接收回调"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            
            print(f"\n📨 收到设备消息:")
            print(f"  主题: {topic}")
            print(f"  QoS: {msg.qos}")
            print(f"  时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 尝试解析JSON
            try:
                json_payload = json.loads(payload)
                print(f"  解析结果:")

                # 使用专业的设备信息解析器
                parsed_info = DeviceInfoParser.parse_basic_info(json_payload)
                print(parsed_info)

                # 如果是设备基本信息，标记为已接收
                if "read" in json_payload and json_payload["read"] == "basicInfo":
                    self.device_info_received = True
                    print("\n✅ 设备基本信息接收完成")

                    # 显示摘要
                    summary = DeviceInfoParser.create_summary(json_payload)
                    print(f"📝 摘要: {summary}")

            except json.JSONDecodeError:
                print(f"  原始数据: {payload}")
            
            print("-" * 80)
            
        except Exception as e:
            print(f"✗ 处理消息时出错: {e}")
    
    def display_device_info(self, data):
        """格式化显示设备信息"""
        print("  设备信息详情:")

        # 如果是字典，逐项显示
        if isinstance(data, dict):
            # 检查是否是设备基本信息响应
            if "read" in data and data["read"] == "basicInfo":
                self.display_basic_info_response(data)
            else:
                # 通用显示方式
                for key, value in data.items():
                    if isinstance(value, dict):
                        print(f"    {key}:")
                        for sub_key, sub_value in value.items():
                            print(f"      {sub_key}: {sub_value}")
                    elif isinstance(value, list):
                        print(f"    {key}: {', '.join(map(str, value))}")
                    else:
                        print(f"    {key}: {value}")
        else:
            print(f"    数据: {data}")

    def display_basic_info_response(self, data):
        """专门显示设备基本信息响应"""
        print("    📋 设备基本信息响应:")
        print("    " + "="*50)

        # 设备标识信息
        print("    🔍 设备标识:")
        if "DeviceID" in data:
            print(f"      设备ID: {data['DeviceID']}")
        if "IMEI" in data:
            print(f"      IMEI: {data['IMEI']}")
        if "IMSI" in data:
            print(f"      IMSI: {data['IMSI']}")
        if "ICCID" in data:
            print(f"      ICCID: {data['ICCID']}")

        # 版本信息
        print("    📦 版本信息:")
        if "version" in data:
            print(f"      软件版本: {data['version']}")
        if "hardVer" in data:
            print(f"      硬件版本: {data['hardVer']}")
        if "type" in data:
            print(f"      设备类型: {data['type']}")

        # 网络信息
        print("    📶 网络状态:")
        if "singnal" in data:
            print(f"      信号强度: {data['singnal']} dBm")
        if "csq" in data:
            print(f"      信号质量: {data['csq']}")

        # 位置信息
        if "location" in data:
            print("    📍 位置信息:")
            location = data['location']
            if '-' in location:
                lat, lon = location.split('-')
                print(f"      纬度: {lat}")
                print(f"      经度: {lon}")
            else:
                print(f"      位置: {location}")

        # 设备状态
        print("    ⚙️  设备状态:")
        if "holdopen" in data:
            holdopen_status = "开启" if data['holdopen'] else "关闭"
            print(f"      保持开门: {holdopen_status}")
        if "delayTime" in data:
            print(f"      延迟时间: {data['delayTime']}秒")
        if "refuseCopy" in data:
            refuse_status = "开启" if data['refuseCopy'] else "关闭"
            print(f"      拒绝复制: {refuse_status}")
        if "keyLock" in data:
            lock_status = "锁定" if data['keyLock'] else "未锁定"
            print(f"      按键锁定: {lock_status}")

        # 时间戳
        if "timestamp" in data:
            import datetime
            timestamp = data['timestamp']
            dt = datetime.datetime.fromtimestamp(timestamp)
            print("    🕐 时间信息:")
            print(f"      时间戳: {timestamp}")
            print(f"      格式化时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")

        # 其他信息
        other_keys = set(data.keys()) - {
            'DeviceID', 'IMEI', 'IMSI', 'ICCID', 'version', 'hardVer', 'type',
            'singnal', 'csq', 'location', 'holdopen', 'delayTime', 'refuseCopy',
            'keyLock', 'timestamp', 'read'
        }
        if other_keys:
            print("    📄 其他信息:")
            for key in sorted(other_keys):
                print(f"      {key}: {data[key]}")

        print("    " + "="*50)
    
    def on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        print(f"✗ 设备信息监听器 {self.client_id} 断开连接，代码: {rc}")
        self.connected = False
    
    def on_subscribe(self, client, userdata, mid, granted_qos):
        """订阅成功回调"""
        print(f"✓ 订阅成功，消息ID: {mid}, QoS: {granted_qos}")
        print("等待设备信息...")
    
    def connect(self):
        """连接到EMQX服务器"""
        try:
            host = EMQX_CONFIG['host']
            port = 1883  # MQTT端口
            
            print(f"正在连接到 {host}:{port}...")
            self.client.connect(host, port, 60)
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def start_listening(self, timeout=30):
        """开始监听设备信息"""
        if self.connect():
            print("开始监听设备信息...")
            print("按 Ctrl+C 退出")
            
            try:
                # 启动循环
                self.client.loop_start()
                
                # 等待连接建立
                wait_time = 0
                while not self.connected and wait_time < 10:
                    time.sleep(1)
                    wait_time += 1
                
                if not self.connected:
                    print("✗ 连接超时")
                    return False
                
                # 等待设备信息或超时
                wait_time = 0
                while not self.device_info_received and wait_time < timeout:
                    time.sleep(1)
                    wait_time += 1
                    if wait_time % 5 == 0:
                        print(f"等待设备信息... ({wait_time}/{timeout}秒)")
                
                if not self.device_info_received:
                    print(f"⚠️  等待{timeout}秒后未收到设备信息")
                    print("可能的原因:")
                    print("- 设备未响应读取命令")
                    print("- 设备响应延迟较高")
                    print("- 网络连接问题")
                
                self.client.loop_stop()
                
            except KeyboardInterrupt:
                print("\n正在断开连接...")
                self.client.loop_stop()
                self.client.disconnect()
                print("已断开连接")
                
            return self.device_info_received
        else:
            print("无法连接到MQTT服务器")
            return False


def request_and_listen_device_info():
    """请求设备信息并监听响应"""
    print("=== 设备信息请求和监听演示 ===")
    print()
    
    # 创建监听器
    listener = DeviceInfoListener()
    
    # 启动监听（非阻塞）
    if listener.connect():
        listener.client.loop_start()
        
        # 等待连接建立
        time.sleep(2)
        
        if listener.connected:
            print("✓ 监听器已就绪，开始请求设备信息...")
            
            # 发送设备信息读取请求
            try:
                response = requests.post("http://127.0.0.1:5000/device/info")
                if response.status_code == 200:
                    result = response.json()
                    print(f"✓ 设备信息请求发送成功")
                    print(f"  消息ID: {result['data']['id']}")
                    print()
                    print("等待设备响应...")
                    
                    # 等待响应
                    wait_time = 0
                    timeout = 30
                    while not listener.device_info_received and wait_time < timeout:
                        time.sleep(1)
                        wait_time += 1
                        if wait_time % 5 == 0:
                            print(f"等待中... ({wait_time}/{timeout}秒)")
                    
                    if listener.device_info_received:
                        print("\n✓ 设备信息接收完成")
                    else:
                        print(f"\n⚠️  等待{timeout}秒后未收到设备响应")
                        
                else:
                    print(f"✗ 设备信息请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"✗ 请求异常: {e}")
            
            listener.client.loop_stop()
        
        listener.client.disconnect()
    else:
        print("✗ 无法建立MQTT连接")


def main():
    """主函数"""
    print("=== 设备信息监听器 ===")
    print()
    print("选择操作模式:")
    print("1. 仅监听设备信息")
    print("2. 请求并监听设备信息")
    print()
    
    try:
        choice = input("请选择 (1 或 2): ").strip()
        
        if choice == '1':
            # 仅监听模式
            listener = DeviceInfoListener()
            listener.start_listening(timeout=60)
            
        elif choice == '2':
            # 请求并监听模式
            request_and_listen_device_info()
            
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序异常: {e}")


if __name__ == '__main__':
    main()
