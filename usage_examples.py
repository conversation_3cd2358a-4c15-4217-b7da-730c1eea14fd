#!/usr/bin/env python3
"""
使用示例脚本
演示如何使用MQTT API服务
"""

import requests
import json
import time
import random

class MQTTAPIClient:
    """MQTT API客户端"""
    
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
    
    def open_door(self):
        """开门"""
        response = requests.post(f"{self.base_url}/door/open")
        return response.json()
    
    def close_door(self):
        """关门"""
        response = requests.post(f"{self.base_url}/door/close")
        return response.json()
    
    def publish_message(self, topic, payload, qos=1):
        """发布消息"""
        data = {
            "topic": topic,
            "payload": payload,
            "qos": qos
        }
        response = requests.post(f"{self.base_url}/publish", json=data)
        return response.json()
    
    def create_subscription(self, clientid, topic, qos=1):
        """创建订阅"""
        data = {
            "clientid": clientid,
            "topic": topic,
            "qos": qos
        }
        response = requests.post(f"{self.base_url}/subscribe", json=data)
        return response.json()
    
    def get_clients(self):
        """获取客户端列表"""
        response = requests.get(f"{self.base_url}/clients")
        return response.json()
    
    def get_client_info(self, clientid):
        """获取客户端信息"""
        response = requests.get(f"{self.base_url}/clients/{clientid}")
        return response.json()

    def set_password(self, passwords, time_start=None, time_stop=None, limit=None, command_id=None):
        """设置设备密码"""
        data = {
            "passwords": passwords
        }
        if time_start is not None:
            data["timeStart"] = time_start
        if time_stop is not None:
            data["timeStop"] = time_stop
        if limit is not None:
            data["limit"] = limit
        if command_id is not None:
            data["id"] = command_id

        response = requests.post(f"{self.base_url}/password/set", json=data)
        return response.json()

    def delete_password(self, passwords):
        """删除设备密码"""
        data = {
            "passwords": passwords,
            "id":''.join(random.choices('0123456789', k=13))
        }
        response = requests.post(f"{self.base_url}/password/delete", json=data)
        return response.json()

    def read_device_info(self):
        """读取设备基本信息"""
        response = requests.post(f"{self.base_url}/device/info")
        return response.json()


def demo_door_control():
    """演示门控制功能"""
    print("=== 门控制演示 ===")
    
    client = MQTTAPIClient()
    
    # 开门
    print("1. 发送开门命令...")
    result = client.open_door()
    if result['success']:
        print(f"✓ 开门命令发送成功，消息ID: {result['data']['id']}")
    else:
        print(f"✗ 开门命令发送失败: {result['message']}")
    
    # 等待3秒
    print("等待3秒...")
    time.sleep(3)
    
    # 关门
    print("2. 发送关门命令...")
    result = client.close_door()
    if result['success']:
        print(f"✓ 关门命令发送成功，消息ID: {result['data']['id']}")
    else:
        print(f"✗ 关门命令发送失败: {result['message']}")
    
    print()


def demo_custom_message(custom_payload):
    """演示自定义消息发布"""
    print("=== 自定义消息演示 ===")
    
    client = MQTTAPIClient()
    
    # # 发布自定义消息
    # custom_payload = {
    #     "action": "Crldoor",
    #     "state": "open",
    #     "holdopen": 0,
    #     "id": ''.join(random.choices('0123456789', k=13))
    # }
    
    print("发送自定义消息...")
    result = client.publish_message("/deviceaccept/2427502F70", custom_payload)
    if result['success']:
        print(f"✓ 自定义消息发送成功，消息ID: {result['data']['id']}")
        print(f"  消息内容: {json.dumps(custom_payload, indent=2, ensure_ascii=False)}")
    else:
        print(f"✗ 自定义消息发送失败: {result['message']}")
    
    print()


def demo_client_management():
    """演示客户端管理"""
    print("=== 客户端管理演示 ===")
    
    client = MQTTAPIClient()
    
    # 获取客户端列表
    print("1. 获取客户端列表...")
    result = client.get_clients()
    if result['success']:
        clients = result['data']['data']
        print(f"✓ 当前有 {len(clients)} 个客户端连接:")
        for i, client_info in enumerate(clients, 1):
            print(f"  {i}. 客户端ID: {client_info['clientid']}")
            print(f"     用户名: {client_info.get('username', 'N/A')}")
            print(f"     IP地址: {client_info['ip_address']}")
            print(f"     连接时间: {client_info['connected_at']}")
            print(f"     订阅数量: {client_info['subscriptions_cnt']}")
            print()
        
        # 获取第一个客户端的详细信息
        if clients:
            first_client_id = clients[0]['clientid']
            print(f"2. 获取客户端 {first_client_id} 的详细信息...")
            detail_result = client.get_client_info(first_client_id)
            if detail_result['success']:
                print("✓ 客户端详细信息获取成功")
                print(f"  协议版本: {detail_result['data']['proto_ver']}")
                print(f"  保持连接: {detail_result['data']['keepalive']}秒")
                print(f"  接收消息数: {detail_result['data']['recv_msg']}")
                print(f"  发送消息数: {detail_result['data']['send_msg']}")
            else:
                print(f"✗ 获取客户端详细信息失败: {detail_result['message']}")
    else:
        print(f"✗ 获取客户端列表失败: {result['message']}")
    
    print()


def demo_device_info():
    """演示读取设备信息功能"""
    print("=== 设备信息读取演示 ===")

    client = MQTTAPIClient()

    # 读取设备基本信息
    print("读取设备基本信息...")
    result = client.read_device_info()
    if result['success']:
        print(f"✓ 设备信息读取命令发送成功")
        print(f"  消息ID: {result['data']['id']}")
        print(f"  注意: {result['note']}")
        print("  设备将通过MQTT主题返回详细信息")
    else:
        print(f"✗ 设备信息读取命令发送失败: {result['message']}")

    print()


def demo_subscription():
    """演示订阅管理"""
    print("=== 订阅管理演示 ===")
    
    client = MQTTAPIClient()
    
    # 首先获取一个已连接的客户端
    clients_result = client.get_clients()
    if clients_result['success']:
        clients = clients_result['data']['data']
        if clients:
            # 找到设备客户端
            device_client = None
            for c in clients:
                if c['clientid'] == '2427502F70':
                    device_client = c
                    break
            
            if device_client:
                clientid = device_client['clientid']
                print(f"为客户端 {clientid} 创建订阅...")
                
                # 创建订阅
                result = client.create_subscription(clientid, "/devicesend/2427502F70")
                if result['success']:
                    print(f"✓ 订阅创建成功")
                    print(f"  主题: {result['data']['topic']}")
                    print(f"  QoS: {result['data']['qos']}")
                else:
                    print(f"✗ 订阅创建失败: {result['message']}")
            else:
                print("✗ 未找到设备客户端 2427502F70")
        else:
            print("✗ 没有找到已连接的客户端")
    else:
        print(f"✗ 获取客户端列表失败: {clients_result['message']}")
    
    print()


def demo_password_management():
    """演示密码管理功能"""
    print("=== 密码管理演示 ===")

    client = MQTTAPIClient()
    current_time = int(time.time())

    # 设置临时密码
    print("1. 设置临时访客密码（1小时有效，使用1次）...")
    result = client.set_password(
        passwords="123456",
        time_start=current_time,
        time_stop=current_time + 3600,  # 1小时后失效
        limit=1,  # 只能使用1次
        command_id="visitor_demo"
    )
    if result['success']:
        print(f"✓ 临时密码设置成功")
        print(f"  消息ID: {result['data']['id']}")
    else:
        print(f"✗ 临时密码设置失败: {result['message']}")

    # 等待2秒
    time.sleep(2)

    # 设置多组密码
    print("2. 设置多组家庭成员密码（永久有效）...")
    result = client.set_password(
        passwords="111111222222333333",  # 3组密码
        command_id="family_demo"
    )
    if result['success']:
        print(f"✓ 多组密码设置成功")
        print(f"  消息ID: {result['data']['id']}")
        print("  密码组数: 3组")
    else:
        print(f"✗ 多组密码设置失败: {result['message']}")

    # 等待2秒
    time.sleep(2)

    # 设置保洁密码
    print("3. 设置保洁人员密码（24小时有效，使用3次）...")
    result = client.set_password(
        passwords="888888",
        time_start=current_time,
        time_stop=current_time + 86400,  # 24小时后失效
        limit=3,  # 可以使用3次
        command_id="cleaner_demo"
    )
    if result['success']:
        print(f"✓ 保洁密码设置成功")
        print(f"  消息ID: {result['data']['id']}")
        print("  有效期: 24小时")
        print("  使用次数: 3次")
    else:
        print(f"✗ 保洁密码设置失败: {result['message']}")

    # 等待2秒
    time.sleep(2)

    # 删除特定密码
    print("4. 删除特定密码...")
    result = client.delete_password(
        passwords="888888" # 删除刚才设置的保洁密码
    )
    if result['success']:
        print(f"✓ 特定密码删除成功")
        print(f"  消息ID: {result['data']['id']}")
    else:
        print(f"✗ 特定密码删除失败: {result['message']}")

    # 等待2秒
    time.sleep(2)

    # 删除所有密码
    print("5. 删除所有密码...")
    result = client.delete_password(
        passwords="all"
    )
    if result['success']:
        print(f"✓ 删除所有密码成功")
        print(f"  消息ID: {result['data']['id']}")
    else:
        print(f"✗ 删除所有密码失败: {result['message']}")

    print()


def setnumpassword(password,num):
    client = MQTTAPIClient()
    # current_time = int(time.time())
     # 设置临时密码
    print("1. 设置临时访客密码（1小时有效，使用1次）...")
    result = client.set_password(
        passwords=password,
        time_start=0,
        time_stop=0,  # 1小时后失效
        limit=num
    )
    if result['success']:
        print(f"✓ 临时密码设置成功")
        print(f"  消息ID: {result['data']['id']}")
    else:
        print(f"✗ 临时密码设置失败: {result['message']}")

def delallpassword(password):
    client = MQTTAPIClient()
    print("5. 删除所有密码...")
    result = client.delete_password(
        passwords=password
    )
    if result['success']:
        print(f"✓ 删除所有密码成功")
        print(f"  消息ID: {result['data']['id']}")
    else:
        print(f"✗ 删除所有密码失败: {result['message']}")

def openthedoor():
    client = MQTTAPIClient()
    
    # 开门
    print("1. 发送开门命令...")
    result = client.open_door()
    if result['success']:
        print(f"✓ 开门命令发送成功，消息ID: {result['data']['id']}")
    else:
        print(f"✗ 开门命令发送失败: {result['message']}")



def getdeviceinfo():
    client = MQTTAPIClient()
    # demo_subscription()
    
    result = client.read_device_info()
    print(result)

def main():
    """主函数"""
    print("=== EMQX MQTT API 使用示例 ===")
    print()
    
    try:
        # 检查API服务是否可用
        response = requests.get("http://127.0.0.1:5000/")
        if response.status_code == 200:
            print("✓ API服务连接正常")
            print()
        else:
            print("✗ API服务连接失败")
            return
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到API服务，请确保Flask应用正在运行")
        return
    
    # 运行各种演示
    # demo_client_management()
    # demo_door_control()
    # demo_custom_message()
    # demo_password_management()
    # demo_device_info()
    # demo_subscription()

    #以下验证完成
    #开门
    # openthedoor()

    #自定义
#     custom_payload={
#     "action": "setkey",
#     "value": "123456",
#     "timeStart": 0,  
#     "timeStop": 0,
#     "limit": 0,
#     "id": "123456789111"
# } 
#     demo_custom_message(custom_payload)


    #设置密码
    # setnumpassword("123456",3)
    #删除密码
    delallpassword("123456")
    print("=== 演示完成 ===")


if __name__ == '__main__':
    main()
